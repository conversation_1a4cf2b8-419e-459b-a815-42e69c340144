use std::{
    path::PathBuf,
    time::{Duration, Instant},
    thread,
};

use crate::realtime_scanner::{RealtimeScanner, ScanStatus};

/// 实时扫描演示
pub struct RealtimeDemo;

impl RealtimeDemo {
    /// 演示实时访问扫描树的功能
    pub fn demo_realtime_access(path: &PathBuf) {
        println!("🔄 实时扫描演示");
        println!("扫描路径: {:?}", path);
        println!("{}", "=".repeat(60));

        let mut scanner = RealtimeScanner::new(500);
        
        // 开始扫描，立即返回根节点引用
        let root = scanner.start_scan(path);
        
        println!("✅ 扫描已开始，可以实时访问扫描树...");
        
        // 实时监控扫描进度
        let start_time = Instant::now();
        let mut last_stats = (0, 0);
        
        for i in 0..50 { // 监控5秒
            thread::sleep(Duration::from_millis(100));
            
            // 获取当前统计
            let (files, dirs) = scanner.get_stats();
            let current_size = root.get_size();
            let children_count = root.get_children_count();
            let status = root.get_scan_status();
            
            // 只在有变化时打印
            if (files, dirs) != last_stats || i % 10 == 0 {
                println!(
                    "⏱️  {:>3}ms | 状态: {:>10?} | 文件: {:>6} | 目录: {:>4} | 大小: {:>10} bytes | 子项: {:>4}",
                    start_time.elapsed().as_millis(),
                    status,
                    files,
                    dirs,
                    current_size,
                    children_count
                );
                last_stats = (files, dirs);
            }
            
            // 如果扫描完成，提前退出
            if status == ScanStatus::Complete {
                println!("🎉 扫描完成！");
                break;
            }
        }
        
        // 显示最终结果
        Self::show_scan_results(&root, &scanner);
        
        // 演示实时查询功能
        Self::demo_realtime_queries(&root);
    }

    /// 显示扫描结果
    fn show_scan_results(root: &std::sync::Arc<crate::realtime_scanner::RealtimeDiskEntry>, scanner: &RealtimeScanner) {
        println!("\n📊 最终扫描结果:");
        println!("{}", "-".repeat(40));
        
        let (files, dirs) = scanner.get_stats();
        let total_size = root.get_size();
        let children_count = root.get_children_count();
        
        println!("📁 根目录: {:?}", root.get_path());
        println!("📈 统计信息:");
        println!("   • 文件数量: {}", files);
        println!("   • 目录数量: {}", dirs);
        println!("   • 总大小: {} bytes ({:.2} MB)", total_size, total_size as f64 / 1024.0 / 1024.0);
        println!("   • 直接子项: {}", children_count);
        println!("   • 扫描状态: {:?}", root.get_scan_status());
    }

    /// 演示实时查询功能
    fn demo_realtime_queries(root: &std::sync::Arc<crate::realtime_scanner::RealtimeDiskEntry>) {
        println!("\n🔍 实时查询演示:");
        println!("{}", "-".repeat(40));
        
        // 获取前5个最大的子项
        println!("📊 前5个最大的子项:");
        let top_children = root.get_top_children(5);
        for (i, child) in top_children.iter().enumerate() {
            let size_mb = child.get_size() as f64 / 1024.0 / 1024.0;
            let name = child.get_path()
                .file_name()
                .map(|n| n.to_string_lossy())
                .unwrap_or_else(|| "Unknown".into());
            let status = child.get_scan_status();
            
            println!(
                "   {}. {} - {:.2} MB ({:?})",
                i + 1, name, size_mb, status
            );
        }
        
        // 获取所有子项的快照
        let all_children = root.get_children_snapshot();
        println!("\n📋 子项详情:");
        println!("   • 总子项数: {}", all_children.len());
        
        let mut complete_count = 0;
        let mut in_progress_count = 0;
        let mut not_started_count = 0;
        
        for child in &all_children {
            match child.get_scan_status() {
                ScanStatus::Complete => complete_count += 1,
                ScanStatus::InProgress => in_progress_count += 1,
                ScanStatus::NotStarted => not_started_count += 1,
                _ => {}
            }
        }
        
        println!("   • 已完成: {}", complete_count);
        println!("   • 扫描中: {}", in_progress_count);
        println!("   • 未开始: {}", not_started_count);
    }

    /// 演示与原版本的对比
    pub fn compare_with_original(path: &PathBuf) {
        println!("⚖️  实时扫描器 vs 原版本对比");
        println!("{}", "=".repeat(60));
        
        // 测试实时版本
        println!("🔄 测试实时版本...");
        let start = Instant::now();
        let mut realtime_scanner = RealtimeScanner::new(500);
        let root = realtime_scanner.start_scan(path);
        
        // 等待完成
        realtime_scanner.wait_for_completion(10000); // 10秒超时
        let realtime_time = start.elapsed();
        let realtime_stats = realtime_scanner.get_stats();
        
        println!("   ⏱️  耗时: {:?}", realtime_time);
        println!("   📊 统计: {} 文件, {} 目录", realtime_stats.0, realtime_stats.1);
        println!("   💾 大小: {} bytes", root.get_size());
        
        // 测试原版本
        println!("\n🔄 测试原版本...");
        let start = Instant::now();
        let original_scanner = crate::scanner::ParallelScanner::default();
        original_scanner.scan(path);
        let original_time = start.elapsed();
        let original_stats = (
            original_scanner.files_processed.load(std::sync::atomic::Ordering::Relaxed),
            original_scanner.dir_processed.load(std::sync::atomic::Ordering::Relaxed),
        );
        
        println!("   ⏱️  耗时: {:?}", original_time);
        println!("   📊 统计: {} 文件, {} 目录", original_stats.0, original_stats.1);
        // 原版本没有直接访问大小的方法，跳过显示
        println!("   💾 大小: [原版本无法直接访问]");
        
        // 对比结果
        println!("\n📈 对比结果:");
        println!("{}", "-".repeat(40));
        let speedup = original_time.as_secs_f64() / realtime_time.as_secs_f64();
        println!("🚀 性能提升: {:.2}x", speedup);
        
        // 功能对比
        println!("\n🔧 功能对比:");
        println!("   实时访问: ✅ 实时版本 | ✅ 原版本");
        println!("   锁竞争:   ✅ 优化(RwLock+批量) | ❌ 严重(Mutex)");
        println!("   内存效率: ✅ 更好(HashMap) | ❌ 较差(嵌套Arc)");
        println!("   并发读取: ✅ 支持 | ❌ 受限");
    }

    /// 压力测试
    pub fn stress_test_realtime_access(path: &PathBuf) {
        println!("💪 实时访问压力测试");
        println!("{}", "=".repeat(60));
        
        let mut scanner = RealtimeScanner::new(1000);
        let root = scanner.start_scan(path);
        
        // 启动多个线程同时读取
        let mut handles = vec![];
        
        for i in 0..4 {
            let root_clone = root.clone();
            let handle = thread::spawn(move || {
                for j in 0..100 {
                    // 并发读取操作
                    let _size = root_clone.get_size();
                    let _count = root_clone.get_children_count();
                    let _status = root_clone.get_scan_status();
                    let _top = root_clone.get_top_children(3);
                    
                    if j % 20 == 0 {
                        println!("🧵 线程 {} 完成 {} 次读取", i, j + 1);
                    }
                    
                    thread::sleep(Duration::from_millis(10));
                }
            });
            handles.push(handle);
        }
        
        // 等待所有线程完成
        for handle in handles {
            handle.join().unwrap();
        }
        
        // 等待扫描完成
        scanner.wait_for_completion(15000);
        
        println!("✅ 压力测试完成！");
        println!("   最终状态: {:?}", root.get_scan_status());
        println!("   最终统计: {:?}", scanner.get_stats());
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_realtime_demo() {
        let current_dir = std::env::current_dir().unwrap();
        RealtimeDemo::demo_realtime_access(&current_dir);
    }

    #[test]
    fn test_comparison() {
        let current_dir = std::env::current_dir().unwrap();
        RealtimeDemo::compare_with_original(&current_dir);
    }

    #[test]
    fn test_stress() {
        let current_dir = std::env::current_dir().unwrap();
        RealtimeDemo::stress_test_realtime_access(&current_dir);
    }
}

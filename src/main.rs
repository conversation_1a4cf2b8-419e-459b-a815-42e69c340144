pub mod scanner;
pub mod async_scanner;
pub mod pool_scanner;
pub mod high_performance_scanner;

use rayon::prelude::*;
use std::{
    collections::{HashMap, HashSet},
    fs,
    path::{Path, PathBuf},
    sync::{
        atomic::{AtomicBool, Ordering}, Arc, Mutex
    },
    time::Instant,
};

use fs_extra::dir::get_size;
use rayon::{ThreadPool, ThreadPoolBuilder};

use crate::{
    high_performance_scanner::{HighPerformanceScanner, IntermediateConfig, IntermediateState, ScanConfig}, scanner::{ParallelScanner, ScanFileInfo}
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("🚀 High-Performance Directory Scanner Demo");
    println!("==========================================");

    // Test path - you can change this to any directory you want to scan
    let test_path = PathBuf::from("/Users/<USER>/h");

    // Demo 1: Basic scan with default configuration
    println!("\n📊 Demo 1: Basic Scan");
    println!("---------------------");
    // demo_basic_scan(&test_path)?;
    // demo_partial_tree(&test_path)?;

    // Demo 2: Custom configuration scan
    // println!("\n⚙️  Demo 2: Custom Configuration Scan");
    // println!("------------------------------------");
    // demo_custom_config_scan(&test_path)?;

    // // Demo 3: Quick scan (statistics only)
    // println!("\n⚡ Demo 3: Quick Scan (Statistics Only)");
    // println!("--------------------------------------");
    // demo_quick_scan(&test_path)?;

    // // Demo 4: Comparison with other scanners
    // println!("\n🏁 Demo 4: Performance Comparison");
    // println!("---------------------------------");
    demo_performance_comparison(&test_path).await?;

    Ok(())
}

fn demo_partial_tree(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();
    
    // Collect partial tree data
    let partial_trees = Arc::new(Mutex::new(Vec::new()));
    let partial_trees_clone = Arc::clone(&partial_trees);
    
    scanner.add_state_callback(move |state: &IntermediateState| {
        if !state.partial_tree.is_empty() {
            let mut trees = partial_trees_clone.lock().unwrap();
            trees.push((
                state.stats.total_files,
                state.partial_tree.len(),
                state.completed_paths.len(),
            ));
        }
    });

    let config = IntermediateConfig {
        callback_interval_ms: 300,
        collect_partial_tree: true,
        max_partial_entries: 100, // Limit to avoid memory issues
        estimate_progress: true,
    };

    let start = Instant::now();
    let result = scanner.scan_with_intermediate(path, config)?;
    let duration = start.elapsed();

    println!("✅ Scan completed in {:?}", duration);
    
    // Show partial tree collection progress
    let trees = partial_trees.lock().unwrap();
    println!("\n🌳 Partial Tree Collection Progress:");
    for (i, (files, tree_entries, completed)) in trees.iter().enumerate() {
        if i % 2 == 0 || i == trees.len() - 1 { // Show every 2nd update + last
            println!("  Update {}: Files processed: {}, Tree entries: {}, Completed paths: {}",
                i + 1, files, tree_entries, completed);
        }
    }

    // Show final intermediate state
    if let Some(final_state) = scanner.get_intermediate_state() {
        println!("\n📋 Final Intermediate State:");
        println!("  Current path: {:?}", final_state.current_path);
        println!("  Partial tree entries: {}", final_state.partial_tree.len());
        println!("  Completed paths: {}", final_state.completed_paths.len());
        println!("  Progress: {:.1}%", final_state.scan_progress * 100.0);
        
        // Show some entries from partial tree
        println!("\n🔍 Sample entries from partial tree:");
        for (i, (path, entry)) in final_state.partial_tree.iter().enumerate() {
            if i >= 5 { break; } // Show first 5 entries
            println!("    {}: {} ({})", 
                i + 1, 
                path.file_name().unwrap_or_default().to_string_lossy(),
                entry.format_size()
            );
        }
    }

    Ok(())
}


fn demo_basic_scan(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();

    let start = Instant::now();
    let result = scanner.scan_and_process(path)?;
    let duration = start.elapsed();

    let stats = scanner.get_stats();

    println!("✅ Scan completed in {:?}", duration);
    println!("📁 Total directories: {}", stats.total_directories);
    println!("📄 Total files: {}", stats.total_files);
    println!("💾 Total size: {}", result.format_size());
    println!("🚀 Speed: {:.0} files/sec, {:.2} MB/sec",
        stats.files_per_second,
        stats.bytes_per_second / (1024.0 * 1024.0)
    );

    println!("\n🔝 Top 5 largest entries:");
    for (i, entry) in result.get_top_entries(5).iter().enumerate() {
        println!("  {}. {} - {}", i + 1, entry.name, entry.format_size());
    }

    Ok(())
}

fn demo_custom_config_scan(path: &PathBuf) -> anyhow::Result<()> {
    let config = ScanConfig {
        max_threads: 8,
        min_file_size_threshold: 10 * 1024, // 10KB threshold
        max_depth: Some(3), // Limit depth to 3 levels
        follow_symlinks: false,
        batch_size: 500,
        show_progress: true,
    };

    let scanner = HighPerformanceScanner::with_config(config);

    let start = Instant::now();
    let result = scanner.scan_and_process(path)?;
    let duration = start.elapsed();

    let stats = scanner.get_stats();

    println!("\n✅ Custom scan completed in {:?}", duration);
    println!("📊 Configuration: 8 threads, 10KB threshold, max depth 3");
    println!("📁 Directories: {}, 📄 Files: {}, 💾 Size: {}",
        stats.total_directories, stats.total_files, result.format_size());

    println!("\n🌳 Directory tree (limited to 2 levels):");
    result.print_tree(2);

    Ok(())
}

fn demo_quick_scan(path: &PathBuf) -> anyhow::Result<()> {
    let scanner = HighPerformanceScanner::new();

    let start = Instant::now();
    let stats = scanner.quick_scan(path)?;
    let duration = start.elapsed();

    println!("⚡ Quick scan completed in {:?}", duration);
    println!("📊 Statistics only (no tree structure built):");
    println!("   📁 Directories: {}", stats.total_directories);
    println!("   📄 Files: {}", stats.total_files);
    println!("   💾 Total size: {:.2} MB", stats.total_size as f64 / (1024.0 * 1024.0));
    println!("   🚀 Speed: {:.0} files/sec", stats.files_per_second);

    Ok(())
}

async fn demo_performance_comparison(path: &PathBuf) -> anyhow::Result<()> {
    println!("Comparing different scanner implementations...\n");

    // High-performance scanner
    // let start = Instant::now();
    // let hp_scanner = HighPerformanceScanner::new();
    // let _hp_result = hp_scanner.quick_scan(path)?;
    // let hp_duration = start.elapsed();
    // let hp_stats = hp_scanner.get_stats();

    // println!("🚀 High-Performance Scanner:");
    // println!("   Time: {:?}", hp_duration);
    // println!("   Files: {}, Dirs: {}", hp_stats.total_files, hp_stats.total_directories);
    // println!("   Speed: {:.0} files/sec", hp_stats.files_per_second);

    // Original parallel scanner (for comparison)
    let start = Instant::now();
    let scanner = ParallelScanner::default();
    scanner.scan(path);

    let parallel_duration = start.elapsed();

    println!("\n📊 Original Parallel Scanner:");
    println!("   Time: {:?}", parallel_duration);

    // Performance comparison
    // let speedup = parallel_duration.as_secs_f64() / hp_duration.as_secs_f64();
    // println!("\n🏆 Performance Improvement: {:.2}x faster", speedup);

    Ok(())
}

struct DiskStat {
    name: String,
    size: Option<u64>,
    children: Vec<DiskStat>,
}

struct DiskScanner {
    size_map: HashMap<PathBuf, u64>,
    path_map: HashMap<PathBuf, HashSet<PathBuf>>,
    pool: Arc<ThreadPool>,
    should_stop: Arc<AtomicBool>,
}

impl DiskScanner {
    fn new() -> Self {
        let pool = ThreadPoolBuilder::new()
            .num_threads(num_cpus::get())
            .build()
            .unwrap();

        Self {
            size_map: HashMap::new(),
            path_map: HashMap::new(),
            pool: Arc::new(pool),
            should_stop: Arc::new(AtomicBool::new(false)),
        }
    }

    pub fn scan(&mut self, path: &PathBuf) {
        self.should_stop.store(false, Ordering::Relaxed);
        let pool = self.pool.clone();
        // pool.install(|| {
        //     self.scan_directory2(path);
        // })
        // self.scan_directory(path).await;
        // tokio::runtime::Runtime::new().unwrap().block_on(async {
        //     self.scan_directory(path);
        // });
    }

    fn scan_directory(&mut self, path: &PathBuf) -> anyhow::Result<()> {
        let result = fs::read_dir(path)?;
        let entries = result.filter_map(|entry| entry.ok()).collect::<Vec<_>>();
        let result = entries
            .par_iter()
            .filter_map(|entry| match entry.file_type() {
                Ok(file_type) if file_type.is_file() => match entry.metadata() {
                    Ok(metadata) => Some((entry.path(), ScanFileInfo::File(metadata.len()))),
                    Err(err) => {
                        println!("{:?}", err);
                        None
                    }
                },
                Ok(file_type) if file_type.is_dir() => {
                    Some((entry.path(), ScanFileInfo::Directory))
                }
                Err(error) => None,
                Ok(f) => {
                    // println!("{:?}", f);
                    None
                }
            })
            .fold(
                || (Vec::new(), Vec::new()),
                |(mut files, mut dirs), (path, info)| {
                    match info {
                        ScanFileInfo::File(size) => {
                            files.push((path.clone(), size));
                        }
                        ScanFileInfo::Directory => {
                            dirs.push(path.clone());
                        }
                    }
                    (files, dirs)
                },
            )
            .reduce(
                || (Vec::new(), Vec::new()),
                |(mut files, mut dirs), (mut files2, mut dirs2)| {
                    files.extend(files2);
                    dirs.extend(dirs2);
                    (files, dirs)
                },
            );

        let (files, directories) = result;
        // self.path_map.insert(path.clone(), );

        // self.size_map
        //     .extend(files.iter().map(|(path, size)| (path.clone(), *size)));

        // let children_paths: HashSet<_> = directories
        //     .iter()
        //     .chain(files.iter().map(|(path, _)| path))
        //     .cloned()
        //     .collect();
        // self.path_map.insert(path.clone(), children_paths);

        // directories.iter().for_each(|path| {
        //     let pool = self.pool.clone();
        //     pool.install(|| self.scan_directory(path));
        // });

        // directories.par_iter().for_each(|path| {
        //     self.scan_directory(path);
        // });



        Ok(())
    }
}

use std::{
    fs,
    path::PathBuf,
    sync::{
        atomic::{AtomicU64, AtomicUsize, Ordering},
        Arc, RwLock,
    },
    collections::HashMap,
    time::Instant,
};

use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Serialize;

/// 支持实时访问的优化 DiskEntry
/// 解决原版本的锁竞争问题，同时保持实时访问能力
#[derive(Debug)]
pub struct RealtimeDiskEntry {
    path: PathBuf,
    is_directory: bool,
    size: AtomicU64,
    parent: Option<Arc<RealtimeDiskEntry>>,
    // 使用 RwLock 替代 Mutex，允许并发读取
    // 使用 HashMap 而不是 Vec，减少锁持有时间
    children: Arc<RwLock<HashMap<PathBuf, Arc<RealtimeDiskEntry>>>>,
    // 添加完成状态标记
    is_complete: Arc<AtomicUsize>, // 0: 未开始, 1: 扫描中, 2: 完成
}

impl RealtimeDiskEntry {
    pub fn new(path: PathBuf, size: u64, is_directory: bool, parent: Option<Arc<RealtimeDiskEntry>>) -> Self {
        Self {
            path,
            is_directory,
            size: AtomicU64::new(size),
            parent,
            children: Arc::new(RwLock::new(HashMap::new())),
            is_complete: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// 添加子节点 - 优化版本
    pub fn add_child(&self, child: Arc<RealtimeDiskEntry>) {
        // 先更新大小（如果是文件）
        if !child.is_directory {
            let size = child.size.load(Ordering::Relaxed);
            self.update_size(size);
        }
        
        // 使用写锁添加子节点，但锁持有时间很短
        {
            let mut children = self.children.write().unwrap();
            children.insert(child.path.clone(), child);
        } // 锁在这里释放
    }

    /// 批量添加子节点 - 减少锁操作次数
    pub fn add_children_batch(&self, children: Vec<Arc<RealtimeDiskEntry>>) {
        let mut total_size = 0u64;
        let mut child_map = HashMap::new();
        
        // 先在锁外准备数据
        for child in children {
            if !child.is_directory {
                total_size += child.size.load(Ordering::Relaxed);
            }
            child_map.insert(child.path.clone(), child);
        }
        
        // 一次性更新大小
        if total_size > 0 {
            self.update_size(total_size);
        }
        
        // 一次性添加所有子节点
        {
            let mut children_guard = self.children.write().unwrap();
            children_guard.extend(child_map);
        }
    }

    fn update_size(&self, size: u64) {
        self.size.fetch_add(size, Ordering::Relaxed);
        if let Some(parent) = &self.parent {
            parent.update_size(size);
        }
    }

    /// 获取当前扫描状态
    pub fn get_scan_status(&self) -> ScanStatus {
        match self.is_complete.load(Ordering::Relaxed) {
            0 => ScanStatus::NotStarted,
            1 => ScanStatus::InProgress,
            2 => ScanStatus::Complete,
            _ => ScanStatus::Unknown,
        }
    }

    /// 设置扫描状态
    pub fn set_scan_status(&self, status: ScanStatus) {
        let value = match status {
            ScanStatus::NotStarted => 0,
            ScanStatus::InProgress => 1,
            ScanStatus::Complete => 2,
            ScanStatus::Unknown => 3,
        };
        self.is_complete.store(value, Ordering::Relaxed);
    }

    /// 实时获取子节点快照 - 支持并发读取
    pub fn get_children_snapshot(&self) -> Vec<Arc<RealtimeDiskEntry>> {
        let children = self.children.read().unwrap();
        children.values().cloned().collect()
    }

    /// 获取子节点数量（无需锁）
    pub fn get_children_count(&self) -> usize {
        let children = self.children.read().unwrap();
        children.len()
    }

    /// 按大小获取前N个子节点
    pub fn get_top_children(&self, n: usize) -> Vec<Arc<RealtimeDiskEntry>> {
        let children = self.children.read().unwrap();
        let mut child_vec: Vec<_> = children.values().cloned().collect();
        child_vec.sort_by(|a, b| b.size.load(Ordering::Relaxed).cmp(&a.size.load(Ordering::Relaxed)));
        child_vec.into_iter().take(n).collect()
    }

    // Getter 方法
    pub fn get_path(&self) -> &PathBuf { &self.path }
    pub fn is_directory(&self) -> bool { self.is_directory }
    pub fn get_size(&self) -> u64 { self.size.load(Ordering::Relaxed) }
    pub fn get_parent(&self) -> Option<Arc<RealtimeDiskEntry>> { self.parent.clone() }
}

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ScanStatus {
    NotStarted,
    InProgress,
    Complete,
    Unknown,
}

/// 支持实时访问的扫描器
pub struct RealtimeScanner {
    files_processed: Arc<AtomicUsize>,
    dirs_processed: Arc<AtomicUsize>,
    batch_size: usize,
    root_entry: Option<Arc<RealtimeDiskEntry>>,
}

impl Default for RealtimeScanner {
    fn default() -> Self {
        Self {
            files_processed: Arc::new(AtomicUsize::new(0)),
            dirs_processed: Arc::new(AtomicUsize::new(0)),
            batch_size: 1000,
            root_entry: None,
        }
    }
}

impl RealtimeScanner {
    pub fn new(batch_size: usize) -> Self {
        Self {
            files_processed: Arc::new(AtomicUsize::new(0)),
            dirs_processed: Arc::new(AtomicUsize::new(0)),
            batch_size,
            root_entry: None,
        }
    }

    /// 开始扫描，返回根节点引用，支持实时访问
    pub fn start_scan(&mut self, path: &PathBuf) -> Arc<RealtimeDiskEntry> {
        let root = Arc::new(RealtimeDiskEntry::new(path.clone(), 0, true, None));
        self.root_entry = Some(root.clone());
        
        // 在后台线程中进行扫描
        let root_clone = root.clone();
        let path_clone = path.clone();
        let files_processed = self.files_processed.clone();
        let dirs_processed = self.dirs_processed.clone();
        let batch_size = self.batch_size;
        
        std::thread::spawn(move || {
            Self::scan_recursive_realtime(
                &path_clone, 
                root_clone, 
                files_processed, 
                dirs_processed, 
                batch_size
            );
        });
        
        root
    }

    /// 递归扫描 - 实时更新版本
    fn scan_recursive_realtime(
        path: &PathBuf,
        parent: Arc<RealtimeDiskEntry>,
        files_processed: Arc<AtomicUsize>,
        dirs_processed: Arc<AtomicUsize>,
        batch_size: usize,
    ) -> anyhow::Result<()> {
        parent.set_scan_status(ScanStatus::InProgress);
        
        let metadata = fs::metadata(path)?;

        if metadata.is_file() {
            let size = metadata.len();
            files_processed.fetch_add(1, Ordering::Relaxed);
            parent.size.store(size, Ordering::Relaxed);
            parent.set_scan_status(ScanStatus::Complete);
            return Ok(());
        }

        if !metadata.is_dir() {
            parent.set_scan_status(ScanStatus::Complete);
            return Ok(());
        }

        dirs_processed.fetch_add(1, Ordering::Relaxed);

        // 读取目录条目
        let entries = fs::read_dir(path)?;
        let mut paths_to_scan = Vec::new();
        
        for entry in entries {
            if let Ok(entry) = entry {
                if let Ok(file_type) = entry.file_type() {
                    if !file_type.is_symlink() {
                        paths_to_scan.push(entry.path());
                    }
                }
            }
        }

        // 批量处理策略
        if paths_to_scan.len() > batch_size {
            // 大目录：分批处理，实时更新
            for chunk in paths_to_scan.chunks(batch_size) {
                let children: Vec<Arc<RealtimeDiskEntry>> = chunk
                    .par_iter()
                    .filter_map(|child_path| {
                        let child_entry = Arc::new(RealtimeDiskEntry::new(
                            child_path.clone(), 
                            0, 
                            child_path.is_dir(), 
                            Some(parent.clone())
                        ));
                        
                        // 递归扫描子项
                        if Self::scan_recursive_realtime(
                            child_path, 
                            child_entry.clone(), 
                            files_processed.clone(), 
                            dirs_processed.clone(), 
                            batch_size
                        ).is_ok() {
                            Some(child_entry)
                        } else {
                            None
                        }
                    })
                    .collect();
                
                // 批量添加到父节点
                parent.add_children_batch(children);
            }
        } else {
            // 小目录：直接并行处理
            let children: Vec<Arc<RealtimeDiskEntry>> = paths_to_scan
                .par_iter()
                .filter_map(|child_path| {
                    let child_entry = Arc::new(RealtimeDiskEntry::new(
                        child_path.clone(), 
                        0, 
                        child_path.is_dir(), 
                        Some(parent.clone())
                    ));
                    
                    if Self::scan_recursive_realtime(
                        child_path, 
                        child_entry.clone(), 
                        files_processed.clone(), 
                        dirs_processed.clone(), 
                        batch_size
                    ).is_ok() {
                        Some(child_entry)
                    } else {
                        None
                    }
                })
                .collect();
            
            parent.add_children_batch(children);
        }

        parent.set_scan_status(ScanStatus::Complete);
        Ok(())
    }

    /// 获取当前根节点
    pub fn get_root(&self) -> Option<Arc<RealtimeDiskEntry>> {
        self.root_entry.clone()
    }

    /// 获取扫描统计
    pub fn get_stats(&self) -> (usize, usize) {
        (
            self.files_processed.load(Ordering::Relaxed),
            self.dirs_processed.load(Ordering::Relaxed),
        )
    }

    /// 等待扫描完成
    pub fn wait_for_completion(&self, timeout_ms: u64) -> bool {
        let start = Instant::now();
        while start.elapsed().as_millis() < timeout_ms as u128 {
            if let Some(root) = &self.root_entry {
                if root.get_scan_status() == ScanStatus::Complete {
                    return true;
                }
            }
            std::thread::sleep(std::time::Duration::from_millis(10));
        }
        false
    }
}

/// 序列化支持
impl Serialize for RealtimeDiskEntry {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        use serde::ser::SerializeStruct;
        
        let mut state = serializer.serialize_struct("RealtimeDiskEntry", 5)?;
        state.serialize_field("name", &self.path.file_name().unwrap_or_default())?;
        state.serialize_field("path", &self.path)?;
        state.serialize_field("size", &self.get_size())?;
        state.serialize_field("is_directory", &self.is_directory)?;
        state.serialize_field("status", &format!("{:?}", self.get_scan_status()))?;
        
        if self.is_directory {
            let children_count = self.get_children_count();
            if children_count > 0 {
                state.serialize_field("children_count", &children_count)?;
                // 只序列化前几个子项以避免序列化问题
                let top_children = self.get_top_children(5);
                let child_names: Vec<String> = top_children.iter()
                    .map(|c| c.path.file_name().unwrap_or_default().to_string_lossy().to_string())
                    .collect();
                state.serialize_field("top_children", &child_names)?;
            }
        }
        state.end()
    }
}

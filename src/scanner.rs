use std::{
    collections::{HashMap, HashSet},
    fs,
    path::PathBuf,
    sync::{atomic::{AtomicU64, AtomicUsize, Ordering}, Arc, Mutex, RwLock},
};

use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Serialize;

#[derive(Debug, Serialize)]
pub struct DiskStat {
    name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    size: Option<u64>,
    #[serde(skip_serializing_if = "Vec::is_empty")]
    children: Vec<DiskStat>,
}

pub enum ScanFileInfo {
    File(u64),
    Directory,
}

#[derive(Debug, Serialize)]
pub struct DiskEntryDisplay {
    path: PathBuf,
    size: usize,
    children: Vec<DiskEntryDisplay>,
}

pub struct DiskEntry {
    path: PathBuf,
    is_directory: bool,
    size: AtomicUsize,
    parent: Option<Arc<DiskEntry>>,
    children: Arc<Mutex<Vec<Arc<DiskEntry>>>>,
}

impl DiskEntry {
    pub fn new(path: PathBuf, size: usize, is_directory: bool, parent: Option<Arc<DiskEntry>>) -> Self {
        Self {
            path: path,
            is_directory: is_directory,
            size: AtomicUsize::new(size),
            parent: parent,
            children: Arc::new(Mutex::new(Vec::new())),
        }
    }

    pub fn add_child(&self, child: Arc<DiskEntry>) {
        if !child.is_directory {
            let size = child.size.load(Ordering::Relaxed);
            self.update_size(size);
        }
        self.children.lock().unwrap().push(child);
    }

    fn update_size(&self, size: usize) {
        self.size.fetch_add(size, Ordering::Relaxed);
        if let Some(parent) = &self.parent {
            parent.update_size(size);
        }
    }
}

impl Default for DiskEntry {
    fn default() -> Self {
        Self {
            path: PathBuf::new(),
            is_directory: false,
            size: AtomicUsize::new(0),
            parent: None,
            children: Arc::new(Mutex::new(Vec::new())),
        }
    }
}


#[derive(Default)]
pub struct ParallelScanner {
    pub size_map: HashMap<PathBuf, u64>,
    pub path_map: HashMap<PathBuf, HashSet<PathBuf>>,

    pub files_processed: Arc<AtomicUsize>,
    pub dir_processed: Arc<AtomicUsize>,
    pub root_entry: Arc<DiskEntry>,
    pub entry_map: Arc<Mutex<HashMap<PathBuf, Arc<DiskEntry>>>>
}

impl ParallelScanner {
    pub fn get_stat(&self, path: &PathBuf) {
        let disk_entry = self.entry_map.lock().unwrap().get(path);
    }

    pub fn scan(&self, path: &PathBuf) {
        let root_entry = Arc::clone(&self.root_entry);
        {
            self.entry_map.lock().unwrap().insert(path.to_path_buf(), Arc::clone(&root_entry));
        }
        self.scan_path(path, 0, Arc::clone(&self.root_entry));
        println!("size {}", self.root_entry.size.load(Ordering::Relaxed));

        let children = self.root_entry.children.lock().unwrap();
        println!("len: {}", children.len());
    }

    pub fn scan_path(&self, path: &PathBuf, depth: usize, parent: Arc<DiskEntry>) -> anyhow::Result<()> {
        let metadata = fs::metadata(path)?;

        if metadata.is_file() {
            let size = metadata.len();
            self.files_processed.fetch_add(1, Ordering::Relaxed);
            let disk_entry = Arc::new(DiskEntry::new(path.to_path_buf(), size as usize, false, Some(parent.clone())));
            parent.add_child(Arc::clone(&disk_entry));

            return Ok(());
        }

        if !metadata.is_dir() {
            return Ok(());
        }

        let disk_entry: Arc<DiskEntry> = Arc::new(DiskEntry::new(path.to_path_buf(), 0, true, Some(parent.clone())));
        parent.add_child(Arc::clone(&disk_entry));
        {
            self.entry_map.lock().unwrap().insert(path.to_path_buf(), Arc::clone(&disk_entry));
        }

        self.dir_processed.fetch_add(1, Ordering::Relaxed);

        let mut paths_to_scan = Vec::new();
        let entries = fs::read_dir(path)?;
        for entry in entries {
            if let Ok(entry) = entry { 
                if !entry.file_type()?.is_symlink() {
                    paths_to_scan.push(entry.path());
                }
            }
        }

        paths_to_scan.par_iter().for_each(|entry| {
            self.scan_path(entry, depth + 1, Arc::clone(&disk_entry));
        });


        Ok(())
    }

}

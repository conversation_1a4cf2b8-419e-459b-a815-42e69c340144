use std::{
    fs,
    path::PathBuf,
    sync::{
        atomic::{AtomicU64, AtomicUsize, Ordering},
        Arc, RwLock, Mutex,
    },
    collections::HashMap,
    time::Instant,
};

use rayon::iter::{IntoParallelRefIterator, ParallelIterator};
use serde::Serialize;

/// 优化版本的 DiskEntry - 解决性能问题
#[derive(Debug)]
pub struct OptimizedDiskEntry {
    path: PathBuf,
    is_directory: bool,
    size: AtomicU64,
    // 使用 Vec 而不是 Arc<Mutex<Vec<Arc<DiskEntry>>>>
    // 在构建完成后不再修改，避免锁竞争
    children: Vec<OptimizedDiskEntry>,
}

impl OptimizedDiskEntry {
    pub fn new_file(path: PathBuf, size: u64) -> Self {
        Self {
            path,
            is_directory: false,
            size: AtomicU64::new(size),
            children: Vec::new(),
        }
    }

    pub fn new_directory(path: PathBuf, children: Vec<OptimizedDiskEntry>) -> Self {
        let total_size = children.iter()
            .map(|child| child.size.load(Ordering::Relaxed))
            .sum();
        
        Self {
            path,
            is_directory: true,
            size: AtomicU64::new(total_size),
            children,
        }
    }

    pub fn get_size(&self) -> u64 {
        self.size.load(Ordering::Relaxed)
    }

    pub fn get_path(&self) -> &PathBuf {
        &self.path
    }

    pub fn is_directory(&self) -> bool {
        self.is_directory
    }

    pub fn get_children(&self) -> &[OptimizedDiskEntry] {
        &self.children
    }
}

/// 高性能扫描器 - 使用批量处理和更好的内存布局
pub struct OptimizedScanner {
    files_processed: Arc<AtomicUsize>,
    dirs_processed: Arc<AtomicUsize>,
    batch_size: usize,
}

impl Default for OptimizedScanner {
    fn default() -> Self {
        Self {
            files_processed: Arc::new(AtomicUsize::new(0)),
            dirs_processed: Arc::new(AtomicUsize::new(0)),
            batch_size: 1000, // 批量处理大小
        }
    }
}

impl OptimizedScanner {
    pub fn new(batch_size: usize) -> Self {
        Self {
            files_processed: Arc::new(AtomicUsize::new(0)),
            dirs_processed: Arc::new(AtomicUsize::new(0)),
            batch_size,
        }
    }

    pub fn scan(&self, path: &PathBuf) -> anyhow::Result<OptimizedDiskEntry> {
        self.scan_recursive(path)
    }

    fn scan_recursive(&self, path: &PathBuf) -> anyhow::Result<OptimizedDiskEntry> {
        let metadata = fs::metadata(path)?;

        if metadata.is_file() {
            let size = metadata.len();
            self.files_processed.fetch_add(1, Ordering::Relaxed);
            return Ok(OptimizedDiskEntry::new_file(path.clone(), size));
        }

        if !metadata.is_dir() {
            return Ok(OptimizedDiskEntry::new_directory(path.clone(), Vec::new()));
        }

        self.dirs_processed.fetch_add(1, Ordering::Relaxed);

        // 批量读取目录条目
        let entries = fs::read_dir(path)?;
        let mut paths_to_scan = Vec::new();
        
        for entry in entries {
            if let Ok(entry) = entry {
                if let Ok(file_type) = entry.file_type() {
                    if !file_type.is_symlink() {
                        paths_to_scan.push(entry.path());
                    }
                }
            }
        }

        // 根据条目数量选择处理策略
        let children = if paths_to_scan.len() > self.batch_size {
            // 大目录：分批并行处理
            self.process_large_directory(paths_to_scan)?
        } else {
            // 小目录：直接并行处理
            self.process_small_directory(paths_to_scan)?
        };

        Ok(OptimizedDiskEntry::new_directory(path.clone(), children))
    }

    fn process_small_directory(&self, paths: Vec<PathBuf>) -> anyhow::Result<Vec<OptimizedDiskEntry>> {
        let children: Vec<OptimizedDiskEntry> = paths
            .par_iter()
            .filter_map(|path| self.scan_recursive(path).ok())
            .collect();
        
        Ok(children)
    }

    fn process_large_directory(&self, paths: Vec<PathBuf>) -> anyhow::Result<Vec<OptimizedDiskEntry>> {
        // 分批处理以减少内存压力
        let children: Vec<OptimizedDiskEntry> = paths
            .chunks(self.batch_size)
            .flat_map(|chunk| {
                chunk
                    .par_iter()
                    .filter_map(|path| self.scan_recursive(path).ok())
                    .collect::<Vec<_>>()
            })
            .collect();
        
        Ok(children)
    }

    pub fn get_stats(&self) -> (usize, usize) {
        (
            self.files_processed.load(Ordering::Relaxed),
            self.dirs_processed.load(Ordering::Relaxed),
        )
    }
}

/// 序列化支持
impl Serialize for OptimizedDiskEntry {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        use serde::ser::SerializeStruct;
        
        let mut state = serializer.serialize_struct("OptimizedDiskEntry", 4)?;
        state.serialize_field("name", &self.path.file_name().unwrap_or_default())?;
        state.serialize_field("path", &self.path)?;
        state.serialize_field("size", &self.get_size())?;
        state.serialize_field("is_directory", &self.is_directory)?;
        if self.is_directory && !self.children.is_empty() {
            state.serialize_field("children", &self.children)?;
        }
        state.end()
    }
}

/// 内存池版本 - 进一步优化内存分配
pub struct PooledScanner {
    scanner: OptimizedScanner,
    // 可以添加对象池来重用 DiskEntry 对象
}

impl PooledScanner {
    pub fn new() -> Self {
        Self {
            scanner: OptimizedScanner::default(),
        }
    }

    pub fn scan(&self, path: &PathBuf) -> anyhow::Result<OptimizedDiskEntry> {
        self.scanner.scan(path)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_optimized_scanner() {
        let scanner = OptimizedScanner::default();
        let current_dir = PathBuf::from(".");
        
        if let Ok(result) = scanner.scan(&current_dir) {
            println!("Scanned: {:?}", result.get_path());
            println!("Size: {} bytes", result.get_size());
            println!("Children: {}", result.get_children().len());
            
            let (files, dirs) = scanner.get_stats();
            println!("Files processed: {}, Directories processed: {}", files, dirs);
        }
    }
}
